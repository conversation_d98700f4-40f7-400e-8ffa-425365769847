<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XML to XLS 转换器</title>
    <!-- 只需要引入 SheetJS 核心库即可 -->
    <script src="https://cdn.sheetjs.com/xlsx-latest/package/dist/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
            background-color: #f0f2f5;
        }
        .converter-card {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 90%;
            max-width: 500px;
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        p {
            color: #666;
            margin-bottom: 25px;
        }
        input[type="file"] {
            display: block;
            margin: 20px auto;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
            width: 100%;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        #status {
            margin-top: 20px;
            font-weight: bold;
            min-height: 24px;
        }
        .status-success, .status-info { color: #28a745; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>

    <div class="converter-card">
        <h1>XML to XLS 转换器</h1>
        <p>将 Excel 的 XML 数据表文件转换为标准的 XLS 格式。</p>
        
        <!-- 只接受 .xml 文件 -->
        <input type="file" id="fileInput" accept=".xml, application/vnd.ms-excel.sheet.macroEnabled.12">
        
        <button id="convertButton">转换并下载 XLS</button>
        
        <div id="status"></div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const convertButton = document.getElementById('convertButton');
        const statusDiv = document.getElementById('status');

        fileInput.addEventListener('change', (e) => {
             if(e.target.files.length > 0) {
                 updateStatus(`已选择文件: ${e.target.files[0].name}`, 'info');
             }
        });

        convertButton.addEventListener('click', handleConversion);

        function handleConversion() {
            const file = fileInput.files[0];

            if (!file) {
                updateStatus('请先选择一个 XML 文件。', 'error');
                return;
            }

            convertButton.disabled = true;
            updateStatus('正在读取文件...', 'info');

            const reader = new FileReader();

            reader.onload = function(e) {
                try {
                    const arrayBuffer = e.target.result;
                    
                    updateStatus('正在解析 XML...', 'info');
                    
                    // 定义解析选项，raw:true 确保数据类型不被意外更改
                    const readOptions = { 
                        type: 'array',
                        raw: true 
                    };

                    const workbook = XLSX.read(arrayBuffer, readOptions);
                    
                    updateStatus('解析成功，正在生成 XLS 文件...', 'info');
                    downloadXLS(workbook, getOutputFilename(file.name));
                    
                    updateStatus('转换成功！文件已开始下载。', 'success');

                } catch (error) {
                    console.error('转换失败:', error);
                    updateStatus(`转换失败: ${error.message}`, 'error');
                } finally {
                    convertButton.disabled = false;
                }
            };
            
            reader.onerror = function(err) {
                 console.error('文件读取失败:', err);
                 updateStatus(`文件读取失败: ${err.message}`, 'error');
                 convertButton.disabled = false;
            }
            
            reader.readAsArrayBuffer(file);
        }
        
        function downloadXLS(workbook, filename) {
            const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' });
            const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }
        
        function getOutputFilename(originalName) {
            // 获取文件名（不含扩展名）
            const baseName = originalName.substring(0, originalName.lastIndexOf('.'));
            return `${baseName}.xls`;
        }

        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = `status-${type}`;
        }
    </script>

</body>
</html>
