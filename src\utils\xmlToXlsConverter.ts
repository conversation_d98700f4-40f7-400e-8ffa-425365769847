import * as XLSX from 'xlsx'

/**
 * 将XML文件转换为XLS文件对象
 * @param xmlFile XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const convertXmlToXlsFile = (xmlFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer

        // 使用XLSX解析XML
        const readOptions = {
          type: 'array' as const,
          raw: true,
        }
        const workbook = XLSX.read(arrayBuffer, readOptions)

        // 转换为XLS格式
        const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' })

        // 创建新的File对象
        const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
        const fileName = xmlFile.name.replace(/\.xml$/i, '.xls')
        const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })

        resolve(xlsFile)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(xmlFile)
  })
}

/**
 * 将CSV文件转换为XLS文件对象
 * @param csvFile CSV文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const convertCsvToXlsFile = (csvFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const csvText = e.target?.result as string

        // 使用XLSX解析CSV
        const workbook = XLSX.read(csvText, {
          type: 'string',
          raw: false, // 保持原始数据格式，避免乱码
          codepage: 65001, // UTF-8编码
        })

        // 转换为XLS格式
        const xlsBinary = XLSX.write(workbook, {
          bookType: 'xls',
          type: 'array',
        })

        // 创建新的File对象
        const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
        const fileName = csvFile.name.replace(/\.csv$/i, '.xls')
        const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })

        resolve(xlsFile)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    // 使用UTF-8编码读取CSV文件
    reader.readAsText(csvFile, 'UTF-8')
  })
}

/**
 * 下载文件到本地
 * @param file 要下载的文件对象
 * @param filename 下载的文件名（可选）
 */
export const downloadFile = (file: File, filename?: string): void => {
  const url = URL.createObjectURL(file)
  const link = document.createElement('a')
  link.href = url
  link.download = filename || file.name
  link.style.display = 'none'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理URL对象
  URL.revokeObjectURL(url)
}
