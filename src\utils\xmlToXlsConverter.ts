import * as XLSX from 'xlsx'

/**
 * 将XML文件转换为XLS文件对象
 * @param xmlFile XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const convertXmlToXlsFile = (xmlFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer

        // 使用XLSX解析XML
        const readOptions = {
          type: 'array' as const,
          raw: true,
        }
        const workbook = XLSX.read(arrayBuffer, readOptions)

        // 转换为XLS格式
        const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' })

        // 创建新的File对象
        const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
        const fileName = xmlFile.name.replace(/\.xml$/i, '.xls')
        const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })

        resolve(xlsFile)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(xmlFile)
  })
}
