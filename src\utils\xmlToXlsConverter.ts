import * as XLSX from 'xlsx'

/**
 * XML转XLS转换器工具类
 * 基于SheetJS库实现XML到XLS格式的转换
 */
export class XmlToXlsConverter {
  /**
   * 将XML文件转换为XLS格式并下载
   * @param file XML文件对象
   * @param onProgress 进度回调函数
   * @returns Promise<boolean> 转换是否成功
   */
  static async convertAndDownload(
    file: File,
    onProgress?: (message: string, type: 'info' | 'success' | 'error') => void
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!file) {
        const errorMsg = '请先选择一个XML文件'
        onProgress?.(errorMsg, 'error')
        reject(new Error(errorMsg))
        return
      }

      // 验证文件类型
      if (!this.isValidXmlFile(file)) {
        const errorMsg = '请选择有效的XML文件'
        onProgress?.(errorMsg, 'error')
        reject(new Error(errorMsg))
        return
      }

      onProgress?.('正在读取文件...', 'info')

      const reader = new FileReader()

      reader.onload = (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer
          
          onProgress?.('正在解析XML...', 'info')
          
          // 定义解析选项，raw:true 确保数据类型不被意外更改
          const readOptions = { 
            type: 'array' as const,
            raw: true 
          }

          const workbook = XLSX.read(arrayBuffer, readOptions)
          
          onProgress?.('解析成功，正在生成XLS文件...', 'info')
          
          const filename = this.getOutputFilename(file.name)
          this.downloadXLS(workbook, filename)
          
          onProgress?.('转换成功！文件已开始下载', 'success')
          resolve(true)

        } catch (error) {
          console.error('转换失败:', error)
          const errorMsg = `转换失败: ${error instanceof Error ? error.message : '未知错误'}`
          onProgress?.(errorMsg, 'error')
          reject(error)
        }
      }
      
      reader.onerror = (err) => {
        console.error('文件读取失败:', err)
        const errorMsg = '文件读取失败'
        onProgress?.(errorMsg, 'error')
        reject(new Error(errorMsg))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 验证是否为有效的XML文件
   * @param file 文件对象
   * @returns boolean
   */
  private static isValidXmlFile(file: File): boolean {
    const validExtensions = ['.xml']
    const validMimeTypes = [
      'text/xml',
      'application/xml',
      'application/vnd.ms-excel.sheet.macroEnabled.12'
    ]
    
    const fileName = file.name.toLowerCase()
    const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext))
    const hasValidMimeType = validMimeTypes.includes(file.type)
    
    return hasValidExtension || hasValidMimeType
  }

  /**
   * 下载XLS文件
   * @param workbook XLSX工作簿对象
   * @param filename 文件名
   */
  private static downloadXLS(workbook: XLSX.WorkBook, filename: string): void {
    const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' })
    const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    URL.revokeObjectURL(link.href)
  }

  /**
   * 生成输出文件名
   * @param originalName 原始文件名
   * @returns string 输出文件名
   */
  private static getOutputFilename(originalName: string): string {
    // 获取文件名（不含扩展名）
    const lastDotIndex = originalName.lastIndexOf('.')
    const baseName = lastDotIndex > 0 ? originalName.substring(0, lastDotIndex) : originalName
    return `${baseName}.xls`
  }

  /**
   * 批量转换XML文件为XLS
   * @param files XML文件数组
   * @param onProgress 进度回调函数
   * @param onComplete 完成回调函数
   */
  static async batchConvert(
    files: File[],
    onProgress?: (current: number, total: number, message: string) => void,
    onComplete?: (successCount: number, failCount: number) => void
  ): Promise<void> {
    let successCount = 0
    let failCount = 0

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      onProgress?.(i + 1, files.length, `正在转换: ${file.name}`)

      try {
        await this.convertAndDownload(file)
        successCount++
      } catch (error) {
        console.error(`转换文件 ${file.name} 失败:`, error)
        failCount++
      }
    }

    onComplete?.(successCount, failCount)
  }
}

/**
 * 简化的转换函数，用于快速调用
 * @param file XML文件
 * @param onProgress 进度回调
 * @returns Promise<boolean>
 */
export const convertXmlToXls = (
  file: File,
  onProgress?: (message: string, type: 'info' | 'success' | 'error') => void
): Promise<boolean> => {
  return XmlToXlsConverter.convertAndDownload(file, onProgress)
}

export default XmlToXlsConverter
