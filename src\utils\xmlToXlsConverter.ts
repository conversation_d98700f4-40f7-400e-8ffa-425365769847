import * as XLSX from 'xlsx'

/**
 * 将XML文件转换为XLS文件对象
 * @param xmlFile XML文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const convertXmlToXlsFile = (xmlFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer

        // 使用XLSX解析XML
        const readOptions = {
          type: 'array' as const,
          raw: true,
        }
        const workbook = XLSX.read(arrayBuffer, readOptions)

        // 转换为XLS格式
        const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' })

        // 创建新的File对象
        const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
        const fileName = xmlFile.name.replace(/\.xml$/i, '.xls')
        const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })

        resolve(xlsFile)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(xmlFile)
  })
}

/**
 * 将CSV文件转换为XLS文件对象
 * @param csvFile CSV文件对象
 * @returns Promise<File> 转换后的XLS文件对象
 */
export const convertCsvToXlsFile = (csvFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    // 先尝试用UTF-8读取，如果失败则尝试GBK
    const tryReadWithEncoding = (encoding: string) => {
      return new Promise<string>((resolveRead, rejectRead) => {
        const reader = new FileReader()

        reader.onload = (e) => {
          const text = e.target?.result as string
          // 检查是否有乱码（简单检测）
          if (encoding === 'UTF-8' && text.includes('�')) {
            rejectRead(new Error('UTF-8编码检测到乱码'))
          } else {
            resolveRead(text)
          }
        }

        reader.onerror = () => rejectRead(new Error('文件读取失败'))

        if (encoding === 'GBK') {
          // 对于GBK编码，先读取为ArrayBuffer然后手动解码
          const arrayReader = new FileReader()
          arrayReader.onload = (e) => {
            try {
              const arrayBuffer = e.target?.result as ArrayBuffer
              const uint8Array = new Uint8Array(arrayBuffer)

              // 尝试使用TextDecoder解码GBK
              let text = ''
              try {
                const decoder = new TextDecoder('gbk')
                text = decoder.decode(uint8Array)
              } catch {
                // 如果GBK解码失败，尝试GB2312
                try {
                  const decoder = new TextDecoder('gb2312')
                  text = decoder.decode(uint8Array)
                } catch {
                  // 最后尝试windows-1252
                  const decoder = new TextDecoder('windows-1252')
                  text = decoder.decode(uint8Array)
                }
              }

              resolveRead(text)
            } catch (error) {
              rejectRead(error as Error)
            }
          }
          arrayReader.onerror = () => rejectRead(new Error('文件读取失败'))
          arrayReader.readAsArrayBuffer(csvFile)
        } else {
          reader.readAsText(csvFile, encoding)
        }
      })
    }

    // 先尝试UTF-8，失败则尝试GBK
    tryReadWithEncoding('UTF-8')
      .catch(() => tryReadWithEncoding('GBK'))
      .then((csvText) => {
        try {
          // 预处理CSV文本，为长数字添加引号以确保被识别为文本
          const processedCsvText = csvText
            .split('\n')
            .map((line, index) => {
              if (index === 0) return line // 保持标题行不变

              return line
                .split(',')
                .map((cell) => {
                  const trimmedCell = cell.trim()
                  // 检查是否为长数字（超过10位的纯数字）
                  if (/^\d{10,}$/.test(trimmedCell)) {
                    // 为长数字添加引号，确保被识别为文本
                    return `"${trimmedCell}"`
                  }
                  return cell
                })
                .join(',')
            })
            .join('\n')

          // 使用XLSX解析处理后的CSV
          const workbook = XLSX.read(processedCsvText, {
            type: 'string',
            raw: false, // 允许类型转换，但长数字已被引号保护
          })

          // 转换为XLS格式
          const xlsBinary = XLSX.write(workbook, {
            bookType: 'xls',
            type: 'array',
          })

          // 创建新的File对象
          const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
          const fileName = csvFile.name.replace(/\.csv$/i, '.xls')
          const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })

          resolve(xlsFile)
        } catch (error) {
          reject(error)
        }
      })
      .catch((error) => {
        reject(new Error(`CSV文件编码识别失败: ${error.message}`))
      })
  })
}

/**
 * 下载文件到本地
 * @param file 要下载的文件对象
 * @param filename 下载的文件名（可选）
 */
export const downloadFile = (file: File, filename?: string): void => {
  const url = URL.createObjectURL(file)
  const link = document.createElement('a')
  link.href = url
  link.download = filename || file.name
  link.style.display = 'none'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理URL对象
  URL.revokeObjectURL(url)
}
