<template>
  <ElDialog
    v-model="dialogVisible"
    title="XML转XLS转换器"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="converter-content">
      <div class="description">
        <ElAlert
          title="功能说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            将Excel的XML数据表文件转换为标准的XLS格式，支持单个文件转换和批量转换。
          </template>
        </ElAlert>
      </div>

      <div class="upload-section">
        <ElUpload
          ref="uploadRef"
          class="xml-upload"
          drag
          :auto-upload="false"
          :multiple="true"
          accept=".xml"
          :file-list="fileList"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
        >
          <ElIcon class="el-icon--upload">
            <UploadFilled />
          </ElIcon>
          <div class="el-upload__text">
            将XML文件拖拽到此处，或<em>点击选择文件</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持.xml格式文件，可选择多个文件进行批量转换
            </div>
          </template>
        </ElUpload>
      </div>

      <div class="file-list" v-if="fileList.length > 0">
        <div class="list-header">
          <span>已选择文件 ({{ fileList.length }})</span>
          <ElButton 
            type="danger" 
            size="small" 
            plain
            @click="clearAllFiles"
          >
            清空全部
          </ElButton>
        </div>
        <div class="list-content">
          <div 
            v-for="(file, index) in fileList" 
            :key="index"
            class="file-item"
          >
            <ElIcon class="file-icon">
              <Document />
            </ElIcon>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
            <ElButton
              type="danger"
              size="small"
              plain
              @click="removeFile(index)"
            >
              移除
            </ElButton>
          </div>
        </div>
      </div>

      <div class="status-section" v-if="statusMessage">
        <ElAlert
          :title="statusMessage"
          :type="statusType"
          :closable="false"
          show-icon
        />
      </div>

      <div class="progress-section" v-if="showProgress">
        <ElProgress
          :percentage="progressPercentage"
          :status="progressStatus"
          :stroke-width="8"
        />
        <div class="progress-text">{{ progressText }}</div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose" :disabled="converting">
          取消
        </ElButton>
        <ElButton 
          type="primary" 
          @click="handleConvert"
          :loading="converting"
          :disabled="fileList.length === 0"
        >
          {{ fileList.length > 1 ? '批量转换' : '转换并下载' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ElDialog,
  ElUpload,
  ElButton,
  ElIcon,
  ElAlert,
  ElProgress,
  ElMessage,
  type UploadFile,
  type UploadFiles,
  type UploadInstance
} from 'element-plus'
import { UploadFilled, Document } from '@element-plus/icons-vue'
import { XmlToXlsConverter } from '@/utils/xmlToXlsConverter'
import { calculateFileSize } from '@/utils/fileUtils'

// 组件引用
const uploadRef = ref<UploadInstance>()

// 对话框状态
const dialogVisible = ref(false)
const converting = ref(false)

// 文件列表
const fileList = ref<UploadFile[]>([])

// 状态信息
const statusMessage = ref('')
const statusType = ref<'success' | 'warning' | 'info' | 'error'>('info')

// 进度相关
const showProgress = ref(false)
const progressPercentage = ref(0)
const progressStatus = ref<'success' | 'exception' | undefined>(undefined)
const progressText = ref('')

// 计算属性
const formatFileSize = computed(() => calculateFileSize)

// 文件变更处理
const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
  clearStatus()
}

// 文件移除处理
const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
  clearStatus()
}

// 上传前验证
const beforeUpload = (file: File) => {
  const isXml = file.name.toLowerCase().endsWith('.xml')
  if (!isXml) {
    ElMessage.error('只能上传XML格式的文件！')
    return false
  }
  
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB！')
    return false
  }
  
  return false // 阻止自动上传
}

// 移除单个文件
const removeFile = (index: number) => {
  fileList.value.splice(index, 1)
  uploadRef.value?.clearFiles()
  // 重新添加剩余文件
  fileList.value.forEach(file => {
    if (file.raw) {
      uploadRef.value?.handleStart(file.raw)
    }
  })
  clearStatus()
}

// 清空所有文件
const clearAllFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
  clearStatus()
}

// 清除状态信息
const clearStatus = () => {
  statusMessage.value = ''
  showProgress.value = false
  progressPercentage.value = 0
  progressStatus.value = undefined
  progressText.value = ''
}

// 更新状态
const updateStatus = (message: string, type: 'success' | 'warning' | 'info' | 'error') => {
  statusMessage.value = message
  statusType.value = type
}

// 更新进度
const updateProgress = (current: number, total: number, message: string) => {
  showProgress.value = true
  progressPercentage.value = Math.round((current / total) * 100)
  progressText.value = `${message} (${current}/${total})`
  
  if (current === total) {
    progressStatus.value = 'success'
  }
}

// 转换处理
const handleConvert = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择XML文件')
    return
  }

  converting.value = true
  clearStatus()

  try {
    if (fileList.value.length === 1) {
      // 单文件转换
      const file = fileList.value[0].raw
      if (!file) {
        throw new Error('文件对象无效')
      }

      await XmlToXlsConverter.convertAndDownload(file, updateStatus)
      
    } else {
      // 批量转换
      const files = fileList.value.map(f => f.raw).filter(Boolean) as File[]
      
      await XmlToXlsConverter.batchConvert(
        files,
        updateProgress,
        (successCount, failCount) => {
          if (failCount === 0) {
            updateStatus(`批量转换完成！成功转换 ${successCount} 个文件`, 'success')
          } else {
            updateStatus(
              `批量转换完成！成功 ${successCount} 个，失败 ${failCount} 个`, 
              'warning'
            )
          }
        }
      )
    }
    
  } catch (error) {
    console.error('转换失败:', error)
    updateStatus(
      `转换失败: ${error instanceof Error ? error.message : '未知错误'}`, 
      'error'
    )
  } finally {
    converting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (converting.value) {
    ElMessage.warning('转换进行中，请稍候...')
    return
  }
  
  dialogVisible.value = false
  clearAllFiles()
  clearStatus()
}

// 打开对话框
const handleOpen = () => {
  dialogVisible.value = true
  clearAllFiles()
  clearStatus()
}

// 暴露方法
defineExpose({
  handleOpen
})
</script>

<style lang="less" scoped>
.converter-content {
  .description {
    margin-bottom: 20px;
  }

  .upload-section {
    margin-bottom: 20px;
    
    .xml-upload {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }
      }
    }
  }

  .file-list {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      background-color: #f5f7fa;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 500;
    }
    
    .list-content {
      max-height: 200px;
      overflow-y: auto;
      
      .file-item {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .file-icon {
          margin-right: 10px;
          color: #909399;
        }
        
        .file-name {
          flex: 1;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .file-size {
          margin-right: 10px;
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }

  .status-section {
    margin-bottom: 20px;
  }

  .progress-section {
    margin-bottom: 20px;
    
    .progress-text {
      margin-top: 8px;
      text-align: center;
      color: #606266;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
