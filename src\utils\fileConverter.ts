import * as XLSX from 'xlsx'

export const convertXmlToXlsFile = (xmlFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer
        const workbook = XLSX.read(arrayBuffer, { type: 'array', raw: true })
        const xlsBinary = XLSX.write(workbook, { bookType: 'xls', type: 'array' })
        const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
        const fileName = xmlFile.name.replace(/\.xml$/i, '.xls')
        const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })
        resolve(xlsFile)
      } catch (error) {
        reject(error)
      }
    }

    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsArrayBuffer(xmlFile)
  })
}

export const convertCsvToXlsFile = (csvFile: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const tryReadWithEncoding = (encoding: string) => {
      return new Promise<string>((resolveRead, rejectRead) => {
        const reader = new FileReader()

        reader.onload = (e) => {
          const text = e.target?.result as string
          if (encoding === 'UTF-8' && text.includes('�')) {
            rejectRead(new Error('UTF-8编码检测到乱码'))
          } else {
            resolveRead(text)
          }
        }

        reader.onerror = () => rejectRead(new Error('文件读取失败'))

        if (encoding === 'GBK') {
          const arrayReader = new FileReader()
          arrayReader.onload = (e) => {
            try {
              const arrayBuffer = e.target?.result as ArrayBuffer
              const uint8Array = new Uint8Array(arrayBuffer)
              let text = ''
              try {
                text = new TextDecoder('gbk').decode(uint8Array)
              } catch {
                try {
                  text = new TextDecoder('gb2312').decode(uint8Array)
                } catch {
                  text = new TextDecoder('windows-1252').decode(uint8Array)
                }
              }
              resolveRead(text)
            } catch (error) {
              rejectRead(error as Error)
            }
          }
          arrayReader.onerror = () => rejectRead(new Error('文件读取失败'))
          arrayReader.readAsArrayBuffer(csvFile)
        } else {
          reader.readAsText(csvFile, encoding)
        }
      })
    }

    tryReadWithEncoding('UTF-8')
      .catch(() => tryReadWithEncoding('GBK'))
      .then((csvText) => {
        try {
          const lines = csvText.trim().split('\n')
          const data: string[][] = lines.map((line) => line.split(',').map((cell) => cell.trim()))

          const workbook = XLSX.utils.book_new()
          const worksheet = XLSX.utils.aoa_to_sheet(data)

          // 强制所有单元格为文本格式
          const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1')
          for (let row = range.s.r; row <= range.e.r; row++) {
            for (let col = range.s.c; col <= range.e.c; col++) {
              const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
              const cell = worksheet[cellAddress]
              if (cell) {
                cell.t = 's'
                cell.z = '@'
                if (cell.v !== undefined && cell.v !== null) {
                  cell.v = cell.v.toString()
                }
              }
            }
          }

          XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
          const xlsBinary = XLSX.write(workbook, {
            bookType: 'xls',
            type: 'array',
            cellStyles: true,
          })
          const blob = new Blob([xlsBinary], { type: 'application/vnd.ms-excel' })
          const fileName = csvFile.name.replace(/\.csv$/i, '.xls')
          const xlsFile = new File([blob], fileName, { type: 'application/vnd.ms-excel' })
          resolve(xlsFile)
        } catch (error) {
          reject(error)
        }
      })
      .catch((error) => {
        reject(new Error(`CSV文件编码识别失败: ${error.message}`))
      })
  })
}
